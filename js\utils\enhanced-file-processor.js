/**
 * Enhanced File Processor
 * Provides crash-resistant folder loading with comprehensive error handling
 */

import StreamingFileReader from './streaming-file-reader.js';
import ResilientWorkerPool from './resilient-worker-pool.js';
import MemoryManager from './memory-manager.js';
import FileProcessingErrorBoundary from './file-processing-error-boundary.js';

class EnhancedFileProcessor {
    constructor(options = {}) {
        this.options = {
            maxConcurrentReads: options.maxConcurrentReads || 10,
            maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 10MB
            chunkSize: options.chunkSize || 100,
            memoryThreshold: options.memoryThreshold || 0.7,
            workerScriptPath: options.workerScriptPath || 'js/core/scanner.worker.js',
            maxWorkers: options.maxWorkers || Math.min(navigator.hardwareConcurrency || 4, 4),
            workerTimeout: options.workerTimeout || 30000,
            enableFallback: options.enableFallback !== false,
            ...options
        };
        
        // Components
        this.streamingFileReader = null;
        this.resilientWorkerPool = null;
        this.memoryManager = null;
        this.errorBoundary = null;
        
        // State
        this.isInitialized = false;
        this.isProcessing = false;
        this.currentOperation = null;
        
        // Statistics
        this.stats = {
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            totalFilesProcessed: 0,
            totalProcessingTime: 0,
            averageProcessingTime: 0
        };
        
        // Bind methods
        this.initialize = this.initialize.bind(this);
        this.processFiles = this.processFiles.bind(this);
        this.cleanup = this.cleanup.bind(this);
    }

    /**
     * Initialize all components
     */
    async initialize() {
        if (this.isInitialized) return;
        
        console.log('Initializing Enhanced File Processor...');
        
        try {
            // Initialize StreamingFileReader
            this.streamingFileReader = new StreamingFileReader({
                maxConcurrentReads: this.options.maxConcurrentReads,
                maxFileSize: this.options.maxFileSize,
                chunkSize: this.options.chunkSize,
                memoryThreshold: this.options.memoryThreshold
            });
            
            // Initialize ResilientWorkerPool
            this.resilientWorkerPool = new ResilientWorkerPool({
                workerScriptPath: this.options.workerScriptPath,
                maxWorkers: this.options.maxWorkers,
                workerTimeout: this.options.workerTimeout,
                messageTimeout: 30000, // 30 seconds for message timeout
                healthCheckInterval: 15000, // 15 seconds - less aggressive health checks
                maxRetries: 3
            });
            await this.resilientWorkerPool.initialize();
            
            // Initialize MemoryManager
            this.memoryManager = new MemoryManager({
                memoryThreshold: 0.8,
                criticalThreshold: 0.9,
                monitoringInterval: 2000,
                enableAutoCleanup: true
            });
            
            // Register cleanup callbacks
            this.registerMemoryCleanupCallbacks();
            this.memoryManager.startMonitoring();
            
            // Initialize ErrorBoundary
            this.errorBoundary = new FileProcessingErrorBoundary({
                maxRetries: 3,
                retryDelay: 1000,
                timeoutDuration: 60000 // 60 seconds for processing timeout
            });
            
            this.isInitialized = true;
            console.log('Enhanced File Processor initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize Enhanced File Processor:', error);
            throw new Error(`Initialization failed: ${error.message}`);
        }
    }

    /**
     * Register memory cleanup callbacks
     */
    registerMemoryCleanupCallbacks() {
        // High priority cleanup
        this.memoryManager.registerCleanup('workerResults', () => {
            if (window.workerResults && window.workerResults.length > 0) {
                console.log('Cleaning up worker results');
                window.workerResults = [];
            }
        }, 'high');
        
        // Medium priority cleanup
        this.memoryManager.registerCleanup('fileCache', () => {
            if (window.scanResults?.fileContents) {
                const keys = Object.keys(window.scanResults.fileContents);
                if (keys.length > 100) {
                    // Keep only recent files
                    const toKeep = keys.slice(-50);
                    const newFileContents = {};
                    toKeep.forEach(key => {
                        newFileContents[key] = window.scanResults.fileContents[key];
                    });
                    window.scanResults.fileContents = newFileContents;
                    console.log(`Cleaned up file cache, kept ${toKeep.length} files`);
                }
            }
        }, 'medium');
        
        // Low priority cleanup
        this.memoryManager.registerCleanup('browserCache', () => {
            // Clear browser caches if available
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        if (name.includes('temp') || name.includes('cache')) {
                            caches.delete(name);
                        }
                    });
                });
            }
        }, 'low');
    }

    /**
     * Process files with enhanced error handling and crash resistance
     * @param {FileList|Array} filesToProcess - Files to process
     * @param {Object} options - Processing options
     * @returns {Promise<Object>} - Processing results
     */
    async processFiles(filesToProcess, options = {}) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        
        if (this.isProcessing) {
            throw new Error('Another processing operation is already in progress');
        }
        
        this.isProcessing = true;
        this.stats.totalOperations++;
        const startTime = Date.now();
        
        const progressCallback = options.progressCallback || (() => {});
        const statusCallback = options.statusCallback || (() => {});
        
        try {
            statusCallback('Preparing files for processing...');

            // Load exclude rules
            const excludeRules = await this.loadExcludeRules();
            const excludeRulesArray = Array.isArray(excludeRules) ? excludeRules :
                                      (excludeRules && Array.isArray(excludeRules.folders)) ? excludeRules.folders : [];

            // Apply exclude rules early to filter out unwanted files
            const originalFileCount = filesToProcess.length;
            const filteredFiles = this.applyExcludeRules(filesToProcess, excludeRules);
            const excludedCount = originalFileCount - filteredFiles.length;

            if (excludedCount > 0) {
                console.log(`Enhanced File Processor: Excluded ${excludedCount} files/folders based on exclude rules`);
                statusCallback(`Excluded ${excludedCount} files, processing ${filteredFiles.length} files...`);
            }

            // Use filtered files for processing
            const processableFiles = filteredFiles;

            // Additional memory cleanup before processing to ensure excluded files are completely removed
            if (this.memoryManager) {
                statusCallback('Performing final memory cleanup...');
                await this.memoryManager.triggerCleanup('high');

                // Force garbage collection before processing
                if (window.gc) {
                    window.gc();
                    console.log('Enhanced File Processor: Final garbage collection before processing');
                }
            }

            // Phase 1: Read files with streaming reader
            statusCallback('Reading files...');
            const fileContents = await this.streamingFileReader.readFilesInChunks(
                processableFiles,
                (progress) => {
                    // Limit file reading progress to 5% to leave room for worker processing
                    const readingProgress = Math.min(5, Math.round((progress.processed / progress.total) * 5));
                    progressCallback({
                        phase: 'reading',
                        processed: progress.processed,
                        total: progress.total,
                        percentage: readingProgress,
                        memoryUsage: progress.memoryUsage
                    });

                    statusCallback(`Reading files: ${progress.processed}/${progress.total}`);
                },
                async (chunkResults, chunkInfo) => {
                    console.log(`Read chunk ${chunkInfo.chunkIndex + 1}/${chunkInfo.totalChunks} with ${chunkResults.length} files`);
                }
            );
            
            if (fileContents.length === 0) {
                throw new Error('No valid files found to process');
            }
            
            // Get file reading statistics
            const readerStats = this.streamingFileReader.getStats();
            console.log('File reading completed:', readerStats);
            
            if (readerStats.errorCount > 0) {
                console.warn(`${readerStats.errorCount} files had errors during reading`);
            }
            
            // Phase 2: Process files with resilient worker pool
            statusCallback(`Processing ${fileContents.length} files...`);
            
            const processingOperation = async () => {
                // Initialize worker
                await this.resilientWorkerPool.processWithRetry({
                    type: 'init',
                    projectRootPath: '',
                    totalFilesToExpect: fileContents.length,
                    excludeRules: excludeRulesArray
                });
                
                // Send file chunks
                const chunkSize = 100;
                let processedChunks = 0;
                const totalChunks = Math.ceil(fileContents.length / chunkSize);
                
                for (let i = 0; i < fileContents.length; i += chunkSize) {
                    const chunk = fileContents.slice(i, Math.min(i + chunkSize, fileContents.length));

                    await this.resilientWorkerPool.processWithRetry({
                        type: 'fileChunk',
                        files: chunk
                    });

                    processedChunks++;
                    // Report chunk sending progress from 5% to 10% (after file reading)
                    if (processedChunks % 5 === 0 || processedChunks === totalChunks) {
                        const chunkProgress = 5 + Math.round((processedChunks / totalChunks) * 5);
                        progressCallback({
                            phase: 'sending',
                            processed: processedChunks,
                            total: totalChunks,
                            percentage: Math.min(10, chunkProgress)
                        });
                    }
                }
                
                // Start processing
                statusCallback('Processing files...');

                // Set up progress listener for worker progress events
                const workerProgressListener = (event) => {
                    const { data } = event.detail;
                    if (data.type === 'processing_progress') {
                        // Worker progress takes over completely, showing the actual file processing progress
                        progressCallback({
                            phase: data.phase || 'processing',
                            processed: data.processed,
                            total: data.total,
                            percentage: Math.round((data.processed / data.total) * 100),
                            filePath: data.filePath
                        });
                    }
                };

                // Add the listener
                this.resilientWorkerPool.addEventListener('worker:message', workerProgressListener);

                try {
                    const result = await this.resilientWorkerPool.processWithRetry({
                        type: 'processAll'
                    });

                    return result;
                } finally {
                    // Remove the listener
                    this.resilientWorkerPool.removeEventListener('worker:message', workerProgressListener);
                }
            };
            
            // Wrap processing in error boundary
            const result = await this.errorBoundary.safeProcess(processingOperation, {
                filePath: 'worker-processing',
                requiresMemory: fileContents.length * 1000, // Rough estimate
                timeout: 600000 // 10 minutes for large projects
            });
            
            if (!result.success) {
                throw new Error(result.error?.message || 'Processing failed');
            }
            
            const scanResult = result.result.data;
            
            // Validate result structure
            if (!scanResult || !scanResult.graph || !scanResult.files) {
                throw new Error('Invalid scan result structure');
            }
            
            // Update statistics
            const processingTime = Date.now() - startTime;
            this.stats.successfulOperations++;
            this.stats.totalFilesProcessed += Object.keys(scanResult.files).length;
            this.stats.totalProcessingTime += processingTime;
            this.stats.averageProcessingTime = this.stats.totalProcessingTime / this.stats.totalOperations;
            
            statusCallback('Processing completed successfully');
            console.log(`Successfully processed ${Object.keys(scanResult.files).length} files in ${processingTime}ms`);
            
            return {
                success: true,
                data: scanResult,
                stats: {
                    processingTime,
                    filesProcessed: Object.keys(scanResult.files).length,
                    readerStats,
                    memoryStats: this.memoryManager.getStats(),
                    workerStats: this.resilientWorkerPool.getStats(),
                    errorStats: this.errorBoundary.getStats()
                }
            };
            
        } catch (error) {
            const processingTime = Date.now() - startTime;
            this.stats.failedOperations++;
            this.stats.totalProcessingTime += processingTime;
            
            console.error('Enhanced file processing failed:', error);
            statusCallback(`Processing failed: ${error.message}`);
            
            // Attempt fallback if enabled
            if (this.options.enableFallback) {
                console.log('Attempting fallback processing...');
                try {
                    const fallbackResult = await this.fallbackProcessing(filesToProcess, options);
                    return fallbackResult;
                } catch (fallbackError) {
                    console.error('Fallback processing also failed:', fallbackError);
                }
            }
            
            return {
                success: false,
                error: {
                    message: error.message,
                    type: error.name || 'ProcessingError',
                    stack: error.stack
                },
                stats: {
                    processingTime,
                    filesProcessed: 0,
                    readerStats: this.streamingFileReader?.getStats(),
                    memoryStats: this.memoryManager?.getStats(),
                    workerStats: this.resilientWorkerPool?.getStats(),
                    errorStats: this.errorBoundary?.getStats()
                }
            };
            
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * Fallback processing using basic methods with memory-aware file exclusion
     */
    async fallbackProcessing(filesToProcess, options = {}) {
        console.log('Using fallback processing method');

        const statusCallback = options.statusCallback || (() => {});
        const progressCallback = options.progressCallback || (() => {});

        statusCallback('Using fallback processing...');

        // Apply exclude rules to fallback processing as well
        const excludeRules = await this.loadExcludeRules();
        const filteredFiles = this.applyExcludeRules(filesToProcess, excludeRules);

        console.log(`Fallback processing: Using ${filteredFiles.length} files after exclusion (from ${filesToProcess.length})`);

        // Simple file reading without streaming
        const fileContents = [];
        for (let i = 0; i < Math.min(filteredFiles.length, 500); i++) { // Limit to 500 files
            const file = filteredFiles[i];
            
            try {
                if (file.size > 1024 * 1024) { // Skip files larger than 1MB
                    continue;
                }
                
                const content = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsText(file);
                });
                
                fileContents.push({
                    path: file.webkitRelativePath || file.name,
                    content: content
                });
                
                if (i % 10 === 0) {
                    progressCallback({
                        phase: 'fallback-reading',
                        processed: i,
                        total: Math.min(filteredFiles.length, 500),
                        percentage: Math.round((i / Math.min(filteredFiles.length, 500)) * 100)
                    });
                }
                
            } catch (error) {
                console.warn(`Error reading file ${file.name}:`, error);
            }
        }
        
        // Create basic result structure
        const result = {
            files: {},
            fileContents: {},
            graph: { nodes: [], edges: [] },
            codeMap: {},
            metadata: {
                totalFiles: fileContents.length,
                processedFiles: fileContents.length,
                timestamp: Date.now(),
                fallback: true
            }
        };
        
        // Basic processing
        fileContents.forEach(file => {
            result.fileContents[file.path] = file.content;
            result.files[file.path] = {
                functions: [],
                variables: [],
                events: [],
                imports: [],
                exports: []
            };
            
            result.graph.nodes.push({
                id: file.path,
                path: file.path,
                name: file.path.split('/').pop(),
                type: file.path.split('.').pop()
            });
        });
        
        statusCallback('Fallback processing completed');

        // Final memory cleanup after fallback processing
        if (this.memoryManager) {
            await this.memoryManager.triggerCleanup('medium');
        }

        // Force garbage collection after fallback processing
        if (window.gc) {
            setTimeout(() => {
                window.gc();
                console.log('Fallback processing: Final garbage collection completed');
            }, 100);
        }

        return {
            success: true,
            data: result,
            fallback: true,
            stats: {
                processingTime: 0,
                filesProcessed: fileContents.length
            }
        };
    }

    /**
     * Load exclude rules
     */
    async loadExcludeRules() {
        try {
            const response = await fetch('exclude.json');
            if (!response.ok) {
                console.warn('Failed to load exclude.json, using defaults');
                return { folders: ["node_modules", ".git", "dist", "build", "__pycache__", "venv", "target", "out", "bin"], files: [] };
            }
            return await response.json();
        } catch (error) {
            console.warn('Error loading exclude.json:', error);
            return { folders: ["node_modules", ".git", "dist", "build", "__pycache__", "venv", "target", "out", "bin"], files: [] };
        }
    }

    /**
     * Apply exclude rules to filter out unwanted files and immediately clean up excluded files from memory
     */
    applyExcludeRules(files, excludeRules) {
        if (!excludeRules) return files;

        const excludedFolders = excludeRules.folders || [];
        const excludedFiles = excludeRules.files || [];

        const filesToProcess = [];
        const excludedFilesList = [];

        // Filter files and collect excluded ones for cleanup
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const relPath = (file.webkitRelativePath || file.name || '').toLowerCase().replace(/\\/g, '/');

            if (!relPath) {
                excludedFilesList.push(file);
                continue;
            }

            const pathParts = relPath.split('/');

            // Check folder exclusions
            const isExcludedFolder = excludedFolders.some(excludedFolder => {
                const lowerExcludedFolder = excludedFolder.toLowerCase().trim();
                if (!lowerExcludedFolder) return false;
                return pathParts.includes(lowerExcludedFolder) ||
                       relPath.startsWith(lowerExcludedFolder + '/') ||
                       relPath.includes('/' + lowerExcludedFolder + '/');
            });

            if (isExcludedFolder) {
                excludedFilesList.push(file);
                continue;
            }

            // Check file exclusions
            const fileName = file.name.toLowerCase();
            const isExcludedFile = excludedFiles.some(pattern => {
                if (pattern.includes('*')) {
                    const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
                    return regex.test(fileName);
                }
                return fileName === pattern.toLowerCase();
            });

            if (isExcludedFile) {
                excludedFilesList.push(file);
                continue;
            }

            // Check allowed extensions
            const allowedExtensions = ['.js', '.mjs', '.ts', '.jsx', '.tsx', '.html', '.htm', '.vue', '.svelte', '.json'];
            if (allowedExtensions.some(ext => fileName.endsWith(ext))) {
                filesToProcess.push(file);
            } else {
                excludedFilesList.push(file);
            }
        }

        // Immediately clean up excluded files from memory with enhanced cleanup
        if (excludedFilesList.length > 0) {
            console.log(`Enhanced File Processor: Cleaning up ${excludedFilesList.length} excluded files from memory`);

            // Enhanced memory cleanup for excluded files
            for (let i = 0; i < excludedFilesList.length; i++) {
                const file = excludedFilesList[i];
                if (file) {
                    // Clear all possible file content and references
                    if (file.stream) {
                        try {
                            file.stream.cancel?.();
                        } catch (e) {
                            // Ignore stream cancellation errors
                        }
                        file.stream = null;
                    }
                    if (file.arrayBuffer) file.arrayBuffer = null;
                    if (file.text) file.text = null;
                    if (file.slice) file.slice = null;

                    // Clear any cached content
                    if (file._content) file._content = null;
                    if (file._text) file._text = null;

                    // Clear file metadata that might hold references
                    if (file.lastModified) file.lastModified = null;
                    if (file.lastModifiedDate) file.lastModifiedDate = null;

                    // Nullify the file reference
                    excludedFilesList[i] = null;
                }
            }

            // Clear the excluded files array completely
            excludedFilesList.length = 0;
            excludedFilesList.splice(0);

            // Trigger memory manager cleanup if available
            if (this.memoryManager) {
                this.memoryManager.triggerCleanup('high').catch(err => {
                    console.warn('Memory manager cleanup failed:', err);
                });
            }

            // Force garbage collection if available
            if (window.gc) {
                setTimeout(() => {
                    window.gc();
                    console.log('Enhanced File Processor: Forced garbage collection after excluding files');
                }, 50); // Reduced timeout for faster cleanup
            }

            console.log('Enhanced File Processor: Excluded files completely removed from memory');
        }

        return filesToProcess;
    }

    /**
     * Get comprehensive statistics
     */
    getStats() {
        return {
            ...this.stats,
            isInitialized: this.isInitialized,
            isProcessing: this.isProcessing,
            components: {
                streamingFileReader: this.streamingFileReader?.getStats(),
                resilientWorkerPool: this.resilientWorkerPool?.getStats(),
                memoryManager: this.memoryManager?.getStats(),
                errorBoundary: this.errorBoundary?.getStats()
            }
        };
    }

    /**
     * Cleanup all components
     */
    async cleanup() {
        console.log('Cleaning up Enhanced File Processor...');
        
        try {
            if (this.memoryManager) {
                this.memoryManager.stopMonitoring();
                await this.memoryManager.manualCleanup();
            }
            
            if (this.resilientWorkerPool) {
                await this.resilientWorkerPool.shutdown();
            }
            
            // Reset state
            this.streamingFileReader = null;
            this.resilientWorkerPool = null;
            this.memoryManager = null;
            this.errorBoundary = null;
            this.isInitialized = false;
            this.isProcessing = false;
            
            console.log('Enhanced File Processor cleanup completed');
            
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }
}

export default EnhancedFileProcessor;